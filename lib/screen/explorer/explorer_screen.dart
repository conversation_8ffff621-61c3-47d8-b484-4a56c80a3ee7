import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/screen/explorer/widget/category_filter_tabs.dart';
import 'package:toii_social/utils/url_luncher/url_luncher.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class ExplorerScreen extends StatefulWidget {
  const ExplorerScreen({super.key});

  @override
  State<ExplorerScreen> createState() => _ExplorerScreenState();
}

class _ExplorerScreenState extends State<ExplorerScreen> {
  String selectedCategory = 'All';

  final List<String> categories = [
    'All',
    'Social',
    'Fitness',
    'Health',
    'AI',
    'Gaming',
  ];

  final List<MarketplaceItemData> marketplaceItems = [
    MarketplaceItemData(
      title: 'BUNNZ #1366',
      price: '0,36 Toii',
      imageAsset: Assets.icons.icMarketplaceItem1.path,
    ),
    MarketplaceItemData(
      title: 'RunxRing – Health',
      price: '\$349',
      imageAsset: Assets.icons.icMarketplaceItem2.path,
    ),
  ];

  final List<DappItemData> dappItems = [
    DappItemData(
      title: 'RunX Ring',
      description: 'RunxRing – Comprehensive Health Identification Ring',
      iconAsset: Assets.icons.icRunxPng.image(),
      backgroundAsset: Assets.icons.icBinance.path,
      url: 'https://apps.apple.com/us/app/runx-ring/id6738332683',
      category: 'Health',
    ),
    DappItemData(
      title: 'Gao Travel',
      description:
          'Book flights, hotels, and global tours with ease. AI recommend...',
      iconAsset: Assets.icons.icGaoTravel.image(),
      backgroundAsset: Assets.icons.icOpensea.path,
      url: 'https://gao.social/',
      category: 'Social',
    ),
    DappItemData(
      title: 'Toii Network',
      description:
          'ToiiNetwork is a Layer2 blockchain infrastructure built ...',
      iconAsset: Assets.icons.icToiiNetwork.image(),
      backgroundAsset: Assets.icons.icTrustWallet.path,
      url: 'https://toii.xyz',
      category: 'Social',
    ),
  ];

  List<DappItemData> get filteredDappItems {
    if (selectedCategory == 'All') {
      return dappItems;
    }
    return dappItems.where((app) => app.category == selectedCategory).toList();
  }

  void _onCategorySelected(String category) {
    setState(() {
      selectedCategory = category;
    });
  }

  void _onDappTap(DappItemData app) {
    launchUrlEnhanced(app.url);
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Scaffold(
      backgroundColor: themeData.schemesOnPrimary,
      extendBodyBehindAppBar: true,
      body: Container(
        height: double.infinity,
        width: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: Assets.images.bgHome.provider(),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header Section
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 6,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Explore',
                      style: headlineSmall.copyWith(
                        color: themeData.textPrimary,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Center(
                        child: SvgPicture.asset(
                          Assets.icons.icSearch.path,
                          width: 24,
                          height: 24,
                          colorFilter: ColorFilter.mode(
                            themeData.textPrimary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Category Filter Tabs
              CategoryFilterTabs(
                categories: categories,
                selectedCategory: selectedCategory,
                onCategorySelected: _onCategorySelected,
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),

                      // Marketplace Section Header
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Marketplace',
                            style: titleLarge.copyWith(
                              color: themeData.textPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SvgPicture.asset(
                            Assets.icons.icArrowRight.path,
                            width: 24,
                            height: 24,
                            colorFilter: ColorFilter.mode(
                              themeData.textSecondary,
                              BlendMode.srcIn,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Marketplace Grid
                      SizedBox(
                        height: 200,
                        child: ListView.separated(
                          scrollDirection: Axis.horizontal,
                          itemCount: marketplaceItems.length,
                          separatorBuilder:
                              (context, index) => const SizedBox(width: 8),
                          itemBuilder: (context, index) {
                            final item = marketplaceItems[index];
                            return Container(
                              width: 180,
                              decoration: BoxDecoration(
                                color: themeData.schemesOnPrimary.withValues(
                                  alpha: 0.8,
                                ),
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                children: [
                                  Expanded(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(16),
                                          topRight: Radius.circular(16),
                                        ),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(16),
                                          topRight: Radius.circular(16),
                                        ),
                                        child: Image.asset(
                                          item.imageAsset,
                                          width: double.infinity,
                                          height: double.infinity,
                                          fit: BoxFit.cover,
                                          errorBuilder: (
                                            context,
                                            error,
                                            stackTrace,
                                          ) {
                                            return Container(
                                              color: themeData.primaryGreen200,
                                              child: const Center(
                                                child: Icon(
                                                  Icons.image_not_supported,
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          item.title,
                                          style: titleMedium.copyWith(
                                            color: themeData.textPrimary,
                                            fontWeight: FontWeight.w600,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          item.price,
                                          style: titleMedium.copyWith(
                                            color: themeData.textPrimary,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),

                      const SizedBox(height: 32),

                      // DApp & Miniapp Section
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Dapp & Miniapp',
                            style: titleLarge.copyWith(
                              color: themeData.textPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SvgPicture.asset(
                            Assets.icons.icArrowRight.path,
                            width: 24,
                            height: 24,
                            colorFilter: ColorFilter.mode(
                              themeData.textSecondary,
                              BlendMode.srcIn,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Dapp Slider
                      CarouselSlider.builder(
                        itemCount: filteredDappItems.length,
                        itemBuilder: (context, index, realIndex) {
                          final app = filteredDappItems[index];
                          return GestureDetector(
                            onTap: () => _onDappTap(app),
                            child: Container(
                              margin: const EdgeInsets.symmetric(horizontal: 6),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Stack(
                                children: [
                                  // Background Image
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(16),
                                    child: Image.asset(
                                      app.backgroundAsset,
                                      width: double.infinity,
                                      height: double.infinity,
                                      fit: BoxFit.cover,
                                      errorBuilder: (
                                        context,
                                        error,
                                        stackTrace,
                                      ) {
                                        return Container(
                                          decoration: BoxDecoration(
                                            color: themeData.primaryGreen200,
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                          ),
                                          child: const Center(
                                            child: Icon(
                                              Icons.image_not_supported,
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                  // Overlay Content
                                  Positioned(
                                    bottom: 0,
                                    left: 0,
                                    right: 0,
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: themeData.neutral100.withValues(
                                          alpha: 0.05,
                                        ),
                                        borderRadius: const BorderRadius.only(
                                          bottomLeft: Radius.circular(16),
                                          bottomRight: Radius.circular(16),
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          // App Icon
                                          Container(
                                            width: 38,
                                            height: 38,
                                            decoration: BoxDecoration(
                                              color: themeData.schemesOnPrimary,
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              child:  app.iconAsset,
                                            ),
                                          ),
                                          const SizedBox(width: 10),
                                          // App Info
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  app.title,
                                                  style: titleMedium.copyWith(
                                                    color: themeData
                                                        .schemesOnPrimary
                                                        .withValues(alpha: 0.9),
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                                Text(
                                                  app.description,
                                                  style: bodySmall.copyWith(
                                                    color: themeData
                                                        .schemesOnPrimary
                                                        .withValues(alpha: 0.8),
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                  maxLines: 2,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                        options: CarouselOptions(
                          height: 177,
                          viewportFraction: 0.7,
                          enableInfiniteScroll: false,
                          enlargeCenterPage: true,
                          enlargeFactor: 0.2,
                          scrollDirection: Axis.horizontal,
                          autoPlay: false,
                          padEnds: false,
                        ),
                      ),

                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MarketplaceItemData {
  final String title;
  final String price;
  final String imageAsset;

  const MarketplaceItemData({
    required this.title,
    required this.price,
    required this.imageAsset,
  });
}

class DappItemData {
  final String title;
  final String description;
  final Widget iconAsset;
  final String backgroundAsset;
  final String url;
  final String category;

  const DappItemData({
    required this.title,
    required this.description,
    required this.iconAsset,
    required this.backgroundAsset,
    required this.url,
    required this.category,
  });
}
